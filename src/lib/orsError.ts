type OrsError = {
  error: {
    code: number;
    message: string;
  };
};

export async function handleOrsError(response: Response): Promise<Error> {
  let errorMessage = 'OpenRouteService API error';
  
  try {
    const errorData: OrsError = await response.json();
    
    switch (response.status) {
      case 400:
        errorMessage = 'Invalid request: Please check your route points';
        break;
      case 401:
        errorMessage = 'API key authentication failed. Please check your API key';
        break;
      case 403:
        errorMessage = 'Access denied: Please check your API key permissions';
        break;
      case 429:
        errorMessage = 'Too many requests: Please try again later';
        break;
      case 500:
        errorMessage = 'OpenRouteService server error: Please try again later';
        break;
      default:
        if (errorData?.error?.message) {
          errorMessage = errorData.error.message;
        }
    }

    // Handle specific ORS error codes if present
    if (errorData?.error?.code) {
      switch (errorData.error.code) {
        case 2000:
          errorMessage = 'No route found between these points. Try different locations.';
          break;
        case 2003:
          errorMessage = 'Route too long, try shorter distances';
          break;
        case 2006:
          errorMessage = 'Unable to find a suitable route. Try different points.';
          break;
        case 2007:
          errorMessage = `No route found for this activity type. Try different points or change activity type.`;
          break;
        case 2008:
          errorMessage = 'Route calculation timeout. Try shorter distances.';
          break;
        case 2009:
          errorMessage = 'Route points are too far apart. Try closer points.';
          break;
        case 2010:
          errorMessage = 'Route points are not reachable by this activity type.';
          break;
      }
    }
  } catch (err) {
    // If we can't parse the error response, return a generic error
    errorMessage = `Service error (${response.status}): Please try again later`;
  }

  return new Error(errorMessage);
}
