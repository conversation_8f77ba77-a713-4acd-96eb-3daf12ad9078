import L from 'leaflet';

interface TrackPoint {
  lat: number;
  lon: number;
  time: string;
  ele: number;
}

interface RouteSegmentWithTime {
  points: TrackPoint[];
  distance: number;
  duration: number;
}

export function generateGPX(
  points: L.LatLng[],
  avgSpeedKmh: number,
  startTime: Date,
  activity: 'Run' | 'Bike'
): string {
  if (points.length < 2) {
    throw new Error('Need at least 2 points to generate GPX');
  }

  // Calculate segment distances and durations
  const segments: RouteSegmentWithTime[] = [];
  let currentTime = new Date(startTime);

  for (let i = 0; i < points.length - 1; i++) {
    const start = points[i];
    const end = points[i + 1];
    const distance = start.distanceTo(end); // in meters
    
    // Calculate duration based on average speed
    const speedMS = avgSpeedKmh * (1000 / 3600); // convert km/h to m/s
    const duration = distance / speedMS; // duration in seconds
    
    // Generate intermediate points (1 point every ~20 meters)
    const numPoints = Math.max(2, Math.ceil(distance / 20));
    const points: TrackPoint[] = [];
    
    for (let j = 0; j < numPoints; j++) {
      const ratio = j / (numPoints - 1);
      const lat = start.lat + (end.lat - start.lat) * ratio;
      const lng = start.lng + (end.lng - start.lng) * ratio;
      const timeOffset = duration * (j / (numPoints - 1));
      const pointTime = new Date(currentTime.getTime() + timeOffset * 1000);
      
      points.push({
        lat,
        lon: lng,
        time: pointTime.toISOString(),
        ele: 0 // Placeholder elevation
      });
    }

    segments.push({
      points,
      distance,
      duration
    });

    currentTime = new Date(currentTime.getTime() + duration * 1000);
  }

  // Generate GPX XML
  const trackpoints = segments.flatMap(segment => segment.points);
  const totalDistance = segments.reduce((sum, segment) => sum + segment.distance, 0);
  const totalDuration = segments.reduce((sum, segment) => sum + segment.duration, 0);

  const gpx = `<?xml version="1.0" encoding="UTF-8"?>
<gpx version="1.1" creator="MapEditor"
  xmlns="http://www.topografix.com/GPX/1/1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.topografix.com/GPX/1/1 http://www.topografix.com/GPX/1/1/gpx.xsd">
  <metadata>
    <name>${activity} Activity</name>
    <time>${startTime.toISOString()}</time>
    <extensions>
      <distance>${totalDistance}</distance>
      <duration>${totalDuration}</duration>
      <type>${activity}</type>
    </extensions>
  </metadata>
  <trk>
    <name>${activity} on ${startTime.toLocaleDateString()}</name>
    <type>${activity}</type>
    <trkseg>
      ${trackpoints.map(point => `
      <trkpt lat="${point.lat.toFixed(6)}" lon="${point.lon.toFixed(6)}">
        <ele>${point.ele}</ele>
        <time>${point.time}</time>
      </trkpt>`).join('')}
    </trkseg>
  </trk>
</gpx>`;

  return gpx;
}

export function downloadGPX(gpxContent: string, filename: string = 'activity.gpx'): void {
  const blob = new Blob([gpxContent], { type: 'application/gpx+xml' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
