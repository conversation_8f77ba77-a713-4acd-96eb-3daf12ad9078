@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  margin: 0;
  padding: 0;
}

/* Ensure Leaflet container takes full size */
.leaflet-container {
  width: 100%;
  height: 100%;
}

/* Ensure Leaflet marker images are visible */
.leaflet-marker-icon,
.leaflet-marker-shadow {
  display: block !important;
}

/* Fix marker z-index to ensure they're above other elements */
.leaflet-marker-pane {
  z-index: 600 !important;
}

.leaflet-popup-pane {
  z-index: 700 !important;
}
