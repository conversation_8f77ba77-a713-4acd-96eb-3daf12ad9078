'use client';

import { useState } from 'react';

export interface ActivityFormData {
  type: 'Run' | 'Bike';
  speed: number;
  startTime: Date;
  isValid: boolean;
}

interface ActivityFormProps {
  onUpdate: (data: ActivityFormData) => void;
}

export default function ActivityForm({ onUpdate }: ActivityFormProps) {
  const [showPace, setShowPace] = useState(false);
  const [formData, setFormData] = useState<ActivityFormData>({
    type: 'Run',
    speed: 10,
    startTime: new Date(),
    isValid: true
  });

  const validateSpeed = (speed: number): boolean => {
    if (formData.type === 'Run') {
      return speed >= 3 && speed <= 25; // 3-25 km/h for running
    }
    return speed >= 5 && speed <= 60; // 5-60 km/h for cycling
  };

  const handleSpeedChange = (value: string) => {
    let speed: number;
    
    try {
      if (showPace) {
        // Convert min/km to km/h
        const [minutes, seconds = 0] = value.split(':').map(Number);
        if (minutes < 0 || seconds < 0 || seconds >= 60) throw new Error('Invalid pace');
        speed = 60 / (minutes + seconds / 60);
      } else {
        speed = Number(value);
        if (isNaN(speed) || speed <= 0) throw new Error('Invalid speed');
      }

      const isValid = validateSpeed(speed);
      const newData = { ...formData, speed, isValid };
      setFormData(newData);
      onUpdate(newData);
    } catch (error) {
      const newData = { ...formData, isValid: false };
      setFormData(newData);
      onUpdate(newData);
    }
  };

  const formatPace = (speedKmh: number): string => {
    const paceMinutes = 60 / speedKmh;
    const minutes = Math.floor(paceMinutes);
    const seconds = Math.round((paceMinutes - minutes) * 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getSpeedError = (): string => {
    if (!formData.isValid) {
      if (formData.type === 'Run') {
        return 'Running speed should be between 3-25 km/h';
      }
      return 'Cycling speed should be between 5-60 km/h';
    }
    return '';
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4">
      <div className="space-y-2">
        <label htmlFor="activity-type" className="block text-sm font-medium">
          Activity Type
        </label>
        <select
          id="activity-type"
          className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600"
          value={formData.type}
          onChange={(e) => {
            const type = e.target.value as 'Run' | 'Bike';
            const isValid = validateSpeed(formData.speed);
            const newData = { ...formData, type, isValid };
            setFormData(newData);
            onUpdate(newData);
          }}
          aria-describedby={!formData.isValid ? 'speed-error' : undefined}
        >
          <option value="Run">Run</option>
          <option value="Bike">Bike</option>
        </select>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <label htmlFor="speed-input" className="text-sm font-medium">
            {showPace ? 'Pace (min/km)' : 'Speed (km/h)'}
          </label>
          <button
            type="button"
            onClick={() => setShowPace(!showPace)}
            className="text-sm text-blue-500 hover:text-blue-600"
            aria-label={`Switch to ${showPace ? 'Speed' : 'Pace'}`}
          >
            Switch to {showPace ? 'Speed' : 'Pace'}
          </button>
        </div>
        <input
          id="speed-input"
          type="text"
          className={`w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600 ${
            !formData.isValid ? 'border-red-500' : ''
          }`}
          value={showPace ? formatPace(formData.speed) : formData.speed}
          onChange={(e) => handleSpeedChange(e.target.value)}
          placeholder={showPace ? '5:30' : '10'}
          aria-invalid={!formData.isValid}
          aria-describedby={!formData.isValid ? 'speed-error' : undefined}
        />
        {!formData.isValid && (
          <p id="speed-error" className="text-sm text-red-500" role="alert">
            {getSpeedError()}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <label htmlFor="start-time" className="block text-sm font-medium">
          Start Time
        </label>
        <input
          id="start-time"
          type="datetime-local"
          className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600"
          value={formData.startTime.toISOString().slice(0, 16)}
          onChange={(e) => {
            const newData = { ...formData, startTime: new Date(e.target.value) };
            setFormData(newData);
            onUpdate(newData);
          }}
        />
      </div>
    </div>
  );
}
