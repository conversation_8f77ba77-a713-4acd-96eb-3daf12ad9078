'use client';

import { useState } from 'react';
import { generateGPX, downloadGPX } from '@/lib/gpxGenerator';
import type { ActivityFormData } from './ActivityForm';
import type { LatLng } from 'leaflet';

interface GPXDownloadButtonProps {
  route: LatLng[];
  activity: ActivityFormData;
  disabled?: boolean;
}

export default function GPXDownloadButton({ route, activity, disabled }: GPXDownloadButtonProps) {
  const [showSuccess, setShowSuccess] = useState(false);

  const handleDownload = () => {
    try {
      const gpxContent = generateGPX(
        route,
        activity.speed,
        activity.startTime,
        activity.type
      );
      
      downloadGPX(gpxContent, `fake-${activity.type.toLowerCase()}-${
        activity.startTime.toISOString().split('T')[0]
      }.gpx`);

      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Error generating GPX:', error);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={handleDownload}
        disabled={disabled || route.length < 2}
        className={`
          px-4 py-2 rounded-lg font-medium shadow
          ${disabled || route.length < 2
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-500 hover:bg-blue-600 text-white'
          }
        `}
      >
        Download GPX
      </button>
      
      {showSuccess && (
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mt--2 px-3 py-1 bg-green-500 text-white text-sm rounded shadow">
          GPX file downloaded!
        </div>
      )}
    </div>
  );
}
