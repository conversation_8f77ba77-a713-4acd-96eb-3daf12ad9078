'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import ActivityForm, { type ActivityFormData } from './ActivityForm';
import GPXDownloadButton from './GPXDownloadButton';
import { handleOrsError } from '@/lib/orsError';

// Define marker icon configuration
const DefaultIcon = L.icon({
  iconUrl: '/images/marker-icon.png',
  iconRetinaUrl: '/images/marker-icon-2x.png',
  shadowUrl: '/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  tooltipAnchor: [16, -28],
  shadowSize: [41, 41]
});

// Set the default icon for all markers
L.Marker.prototype.options.icon = DefaultIcon;

type Waypoint = L.LatLng;
type RouteSegment = {
  coordinates: L.LatLng[];
  distance: number;
};

const MAX_ROUTE_DISTANCE_KM = 30; // OpenRouteService free tier limit

const calculateHaversineDistance = (start: Waypoint, end: Waypoint): number => {
  const R = 6371; // Earth's radius in km
  const dLat = (end.lat - start.lat) * Math.PI / 180;
  const dLon = (end.lng - start.lng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(start.lat * Math.PI / 180) * Math.cos(end.lat * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

export default function MapEditor() {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const [waypoints, setWaypoints] = useState<Waypoint[]>([]);
  const [routeSegments, setRouteSegments] = useState<RouteSegment[]>([]);
  const [totalDistance, setTotalDistance] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mapReady, setMapReady] = useState(false);
  const [activityData, setActivityData] = useState<ActivityFormData>({
    type: 'Run',
    speed: 10,
    startTime: new Date(),
    isValid: true
  });
  
  const markersRef = useRef<L.Marker[]>([]);
  const polylineRef = useRef<L.Polyline | null>(null);

  const fetchRouteSegment = useCallback(async (start: Waypoint, end: Waypoint): Promise<RouteSegment> => {
    try {
      // Check straight-line distance before making API call
      const straightLineDistance = calculateHaversineDistance(start, end);
      if (straightLineDistance > MAX_ROUTE_DISTANCE_KM) {
        throw new Error(`Distance between points (${straightLineDistance.toFixed(1)} km) exceeds the maximum allowed (${MAX_ROUTE_DISTANCE_KM} km). Please add intermediate points.`);
      }

      const profile = activityData.type === 'Bike' ? 'cycling-regular' : 'foot-walking';
      const apiKey = process.env.NEXT_PUBLIC_ORS_API_KEY || '';
      const url = `https://api.openrouteservice.org/v2/directions/${profile}?api_key=${apiKey}&start=${start.lng},${start.lat}&end=${end.lng},${end.lat}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8'
        }
      });

      if (!response.ok) {
        throw await handleOrsError(response);
      }

      const data = await response.json();
      
      // Validate response structure
      if (!data?.features?.[0]?.geometry?.coordinates || !Array.isArray(data.features[0].geometry.coordinates)) {
        console.error('Invalid ORS response:', data);
        throw new Error('Invalid route data received from the server');
      }

      const feature = data.features[0];
      const coordinates = feature.geometry.coordinates.map(
        ([lng, lat]: number[]) => {
          if (typeof lng !== 'number' || typeof lat !== 'number' || isNaN(lng) || isNaN(lat)) {
            throw new Error('Invalid coordinates in route data');
          }
          return new L.LatLng(lat, lng);
        }
      );

      // Extract distance from the response
      const distance = feature.properties?.segments?.[0]?.distance;
      if (typeof distance !== 'number' || isNaN(distance)) {
        throw new Error('Route distance information is missing or invalid');
      }
      
      return { coordinates, distance };
    } catch (error) {
      console.error('Error fetching route:', error);
      let errorMessage = error instanceof Error ? error.message : 'Failed to fetch route';
      
      // Add more context for specific error cases
      if (errorMessage.includes('coordinates')) {
        errorMessage = 'Invalid route points. Please try different locations.';
      } else if (errorMessage.includes('No route found')) {
        errorMessage = 'No route found between these points. Try closer locations.';
      } else if (errorMessage.includes('distance')) {
        errorMessage = 'Could not calculate route distance. Please try again.';
      }
      
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [activityData.type]);

  const updateRouteForWaypoint = useCallback(async (index: number) => {
    if (waypoints.length < 2) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const newSegments = [...routeSegments];
      
      // Update segment before the moved waypoint
      if (index > 0) {
        const start = waypoints[index - 1];
        const end = waypoints[index];
        newSegments[index - 1] = await fetchRouteSegment(start, end);
      }
      
      // Update segment after the moved waypoint
      if (index < waypoints.length - 1) {
        const start = waypoints[index];
        const end = waypoints[index + 1];
        newSegments[index] = await fetchRouteSegment(start, end);
      }
      
      setRouteSegments(newSegments);
      
      const totalDist = newSegments.reduce((sum, segment) => sum + segment.distance, 0);
      setTotalDistance(totalDist);
    } catch (error) {
      console.error('Error updating route:', error);
    } finally {
      setIsLoading(false);
    }
  }, [waypoints, routeSegments, fetchRouteSegment]);

  const handleMapClick = useCallback(async (e: L.LeafletMouseEvent) => {
    console.log('Map clicked at:', e.latlng);
    const newWaypoint = e.latlng;
    
    // If we already have waypoints, check the distance to the last one
    if (waypoints.length > 0) {
      const lastWaypoint = waypoints[waypoints.length - 1];
      const distance = calculateHaversineDistance(lastWaypoint, newWaypoint);
      console.log('Distance to last waypoint:', distance, 'km');
      
      if (distance > MAX_ROUTE_DISTANCE_KM) {
        setError(`This point is too far (${distance.toFixed(1)} km) from the previous point. Maximum allowed distance is ${MAX_ROUTE_DISTANCE_KM} km. Please add intermediate points.`);
        return;
      }
    }
    
    const newWaypoints = [...waypoints, newWaypoint];
    setWaypoints(newWaypoints);
    console.log('New waypoint added, total waypoints:', newWaypoints.length);
    
    if (!mapInstanceRef.current) return;
    
    // Create and add the marker immediately with proper options
    const marker = L.marker(newWaypoint, {
      draggable: true,
      icon: DefaultIcon,
      interactive: true,
      autoPan: true
    }).addTo(mapInstanceRef.current);
    
    markersRef.current.push(marker);
    
    if (newWaypoints.length > 1) {
      setIsLoading(true);
      setError(null);
      
      try {
        const start = newWaypoints[newWaypoints.length - 2];
        const end = newWaypoint;
        const newSegment = await fetchRouteSegment(start, end);
        
        setRouteSegments(prev => [...prev, newSegment]);
        setTotalDistance(prev => prev + newSegment.distance);
        
        // Draw the route line immediately after getting the segment
        const allCoordinates = [...routeSegments, newSegment].flatMap(segment => segment.coordinates);
        if (polylineRef.current) {
          polylineRef.current.remove();
        }
        polylineRef.current = L.polyline(allCoordinates, {
          color: '#3B82F6',
          weight: 4,
          opacity: 0.8,
          interactive: true
        }).addTo(mapInstanceRef.current);
        
      } catch (error) {
        // Remove the waypoint and marker if route fetching fails
        setWaypoints(prev => prev.slice(0, -1));
        marker.remove();
        markersRef.current.pop();
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch route';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    }
  }, [waypoints, routeSegments, fetchRouteSegment]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Backspace' && !e.ctrlKey && !e.metaKey && document.activeElement?.tagName !== 'INPUT') {
      e.preventDefault();
      setWaypoints(prev => prev.slice(0, -1));
      setRouteSegments(prev => prev.slice(0, -1));
      setTotalDistance(prev => 
        prev - (routeSegments[routeSegments.length - 1]?.distance || 0)
      );

      if (mapInstanceRef.current && waypoints.length > 1) {
        const bounds = L.latLngBounds(waypoints.slice(0, -1));
        mapInstanceRef.current.fitBounds(bounds, { padding: [50, 50] });
      }
    }
  }, [waypoints, routeSegments]);

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    const map = L.map(mapRef.current, {
      preferCanvas: true
    }).setView([41.3851, 2.1734], 13);
    mapInstanceRef.current = map;

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    map.on('load', () => {
      setMapReady(true);
    });

    map.on('click', handleMapClick);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      if (mapInstanceRef.current) {
        map.off('click', handleMapClick);
        document.removeEventListener('keydown', handleKeyDown);
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [handleMapClick, handleKeyDown]);

  useEffect(() => {
    const map = mapInstanceRef.current;
    if (!map || !mapReady) return;

    // Clear existing markers and polylines
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];

    if (polylineRef.current) {
      polylineRef.current.remove();
      polylineRef.current = null;
    }

    // Add markers for each waypoint
    waypoints.forEach((waypoint, index) => {
      const marker = L.marker(waypoint, {
        draggable: true,
        icon: DefaultIcon,
        interactive: true,
        autoPan: true
      })
      .on('dragend', () => updateRouteForWaypoint(index))
      .addTo(map);
      
      markersRef.current.push(marker);
    });

    // Draw route polyline
    if (routeSegments.length > 0) {
      const allCoordinates = routeSegments.flatMap(segment => segment.coordinates);
      polylineRef.current = L.polyline(allCoordinates, {
        color: '#3B82F6',
        weight: 4,
        opacity: 0.8,
        interactive: true
      }).addTo(map);

      // Fit bounds to show all points
      if (waypoints.length > 1) {
        try {
          const bounds = L.latLngBounds(waypoints);
          map.fitBounds(bounds, { 
            padding: [50, 50],
            maxZoom: 16 // Prevent excessive zoom when points are close
          });
        } catch (e) {
          console.error('Error fitting bounds:', e);
        }
      }
    }
  }, [waypoints, routeSegments, updateRouteForWaypoint, mapReady]);

  const getAllPoints = (): L.LatLng[] => {
    return routeSegments.flatMap(segment => segment.coordinates);
  };

  return (
    <div className="relative w-full h-full bg-gray-50 dark:bg-gray-900">
      <div ref={mapRef} className="w-full h-full" />
      
      <div className="absolute top-4 left-4 z-[1000] w-80">
        <ActivityForm onUpdate={setActivityData} />
      </div>

      <div className="absolute top-4 right-4 z-[1000] space-y-4">
        {totalDistance > 0 && (
          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-3 rounded-lg shadow-lg text-center font-medium">
            Distance: {(totalDistance / 1000).toFixed(2)} km
          </div>
        )}
        <GPXDownloadButton
          route={getAllPoints()}
          activity={activityData}
          disabled={!activityData.isValid || waypoints.length < 2 || isLoading}
        />
      </div>

      {/* Loading and Error States with improved styling */}
      {isLoading && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm px-4 py-2 rounded-lg shadow-lg z-[1000] flex items-center space-x-2">
          <svg className="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Calculating route...</span>
        </div>
      )}
      {error && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-[1000] max-w-md text-center flex items-center space-x-2">
          <svg className="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  );
}
